<?php
/**
 * Comprehensive script to force-run VedMG ClassRoom DB upgrades and ensure all tables/columns are present.
 *
 * Usage (Windows / XAMPP):
 *   C:\xampp\php\php.exe "c:\xampp\htdocs\paylearn\wp-content\plugins\VedMG-ClassRoom\database\force_db_upgrade.php"
 */

// Show errors during CLI run
error_reporting(E_ALL);
ini_set('display_errors', '1');

// Resolve WordPress root from this file location: \wp-content\plugins\VedMG-ClassRoom\database -> go 4 levels up
$wpLoad = dirname(__DIR__, 4) . DIRECTORY_SEPARATOR . 'wp-load.php';
if (!file_exists($wpLoad)) {
    fwrite(STDERR, "ERROR: Cannot find wp-load.php at: {$wpLoad}\n");
    exit(1);
}

require_once $wpLoad; // Defines ABSPATH, loads WP, sets up $wpdb

// After WP bootstrap we can safely include plugin files
$pluginDir = dirname(__DIR__) . DIRECTORY_SEPARATOR; // VedMG-ClassRoom\

// Include logger if available (for vedmg_log_* helpers)
$loggerFile = $pluginDir . 'Debug' . DIRECTORY_SEPARATOR . 'debugLog.php';
if (file_exists($loggerFile)) {
    require_once $loggerFile;
}

// Include upgrade class
$upgradeFile = $pluginDir . 'database' . DIRECTORY_SEPARATOR . 'upgrade.php';
if (!file_exists($upgradeFile)) {
    fwrite(STDERR, "ERROR: Cannot find upgrade.php at: {$upgradeFile}\n");
    exit(1);
}
require_once $upgradeFile;

// Helper to print both to console and to plugin logger (if available)
function outln($msg) {
    echo $msg . PHP_EOL;
    if (function_exists('vedmg_log_info')) {
        vedmg_log_info('CLI-UPGRADE', $msg);
    }
}

function list_columns($full_table) {
    global $wpdb;
    $cols = $wpdb->get_results("SHOW COLUMNS FROM {$full_table}", ARRAY_A);
    if (empty($cols)) {
        outln("(no columns or table missing)");
        return;
    }
    foreach ($cols as $c) {
        outln(" - {$c['Field']} ({$c['Type']})");
    }
}

function check_and_add_column($table, $column, $definition, $after = null) {
    global $wpdb;

    $exists = $wpdb->get_results("SHOW COLUMNS FROM {$table} LIKE '{$column}'");
    if (empty($exists)) {
        $after_clause = $after ? " AFTER {$after}" : "";
        $sql = "ALTER TABLE {$table} ADD COLUMN {$column} {$definition}{$after_clause}";
        $res = $wpdb->query($sql);
        if ($res !== false) {
            outln("✓ Added missing column: {$table}.{$column}");
            if (function_exists('vedmg_log_database')) {
                vedmg_log_database('ALTER', $table, "Added {$column} column via CLI script");
            }
            return true;
        } else {
            outln("✗ Failed to add column {$table}.{$column}: " . $wpdb->last_error);
            return false;
        }
    } else {
        outln("✓ Column exists: {$table}.{$column}");
        return true;
    }
}

outln('=== VedMG ClassRoom: Comprehensive DB Upgrade Script ===');

// Optionally enable plugin debug logging for this run
if (function_exists('update_option')) {
    update_option('vedmg_classroom_debug_enabled', 1);
}

// Step 1: Run the official upgrade system
$upgrade_ok = false;
if (class_exists('VedMG_ClassRoom_Database_Upgrade')) {
    outln('Step 1: Running VedMG_ClassRoom_Database_Upgrade::force_upgrade() ...');
    try {
        $upgrade_ok = VedMG_ClassRoom_Database_Upgrade::force_upgrade();
        outln('force_upgrade() returned: ' . ($upgrade_ok ? 'true' : 'false'));
        // Normalize/migrate any synonym columns to canonical ones
        if (method_exists('VedMG_ClassRoom_Database_Upgrade', 'normalize_sessions_columns')) {
            VedMG_ClassRoom_Database_Upgrade::normalize_sessions_columns();
            outln('normalize_sessions_columns() completed');
        }
    } catch (Throwable $e) {
        outln('Exception during force_upgrade: ' . $e->getMessage());
    }
} else {
    outln('ERROR: Upgrade class not found.');
}

// Step 2: Ensure all essential tables exist
outln('Step 2: Verifying essential tables exist...');
global $wpdb;

$essential_tables = [
    'vedmg_courses',
    'vedmg_student_enrollments',
    'vedmg_class_sessions',
    'vedmg_instructor_sync'
];

$missing_tables = [];
foreach ($essential_tables as $table) {
    $full_table = $wpdb->prefix . $table;
    $exists = $wpdb->get_var("SHOW TABLES LIKE '{$full_table}'");
    if ($exists !== $full_table) {
        $missing_tables[] = $table;
        outln("✗ Missing table: {$full_table}");
    } else {
        outln("✓ Table exists: {$full_table}");
    }
}

if (!empty($missing_tables)) {
    outln('Attempting to create missing tables via activator...');
    $activatorFile = $pluginDir . 'database' . DIRECTORY_SEPARATOR . 'activator.php';

    if (file_exists($activatorFile)) {
        require_once $activatorFile;
        if (class_exists('VedMG_ClassRoom_Database_Activator')) {
            VedMG_ClassRoom_Database_Activator::activate();
            outln('Activator executed to create missing tables.');
        }
    }
}

// Step 3: Comprehensive column verification for all tables
outln('Step 3: Verifying all required columns exist...');

try {
    // vedmg_class_sessions - Most complex table with many upgrade additions
    $sessions_table = $wpdb->prefix . 'vedmg_class_sessions';
    outln('Checking vedmg_class_sessions columns...');

    $sessions_columns = [
        'assigned_instructor_id' => 'BIGINT(20) UNSIGNED DEFAULT NULL',
        'is_featured' => 'TINYINT(1) DEFAULT 0',
        'featured_date' => 'DATETIME DEFAULT NULL',
        'session_type' => "ENUM('individual','group','class') DEFAULT 'individual'",
        'duration_minutes' => 'INT(11) DEFAULT 60',
        'is_recurring' => 'TINYINT(1) DEFAULT 0',
        'recurring_pattern' => 'VARCHAR(50) DEFAULT NULL',
        'recurring_days' => 'JSON DEFAULT NULL',
        'recurring_dates' => 'JSON DEFAULT NULL',
        'recurring_count' => 'INT(11) DEFAULT 1',
        'recurring_end_date' => 'DATE DEFAULT NULL',
        'selected_student_ids' => 'JSON DEFAULT NULL',
        'meeting_type' => "ENUM('individual', 'class') DEFAULT 'class'",
        'target_student_id' => 'INT NULL'
    ];

    foreach ($sessions_columns as $col => $def) {
        check_and_add_column($sessions_table, $col, $def);
    }

    // Ensure session_status enum includes 'google classroom'
    $status_column = $wpdb->get_row("SHOW COLUMNS FROM {$sessions_table} LIKE 'session_status'");
    if ($status_column && strpos($status_column->Type, 'google classroom') === false) {
        $sql = "ALTER TABLE {$sessions_table} MODIFY COLUMN session_status ENUM('scheduled','ongoing','completed','cancelled','google classroom') DEFAULT 'scheduled'";
        $res = $wpdb->query($sql);
        if ($res !== false) {
            outln("✓ Updated session_status enum to include 'google classroom'");
        } else {
            outln("✗ Failed to update session_status enum: " . $wpdb->last_error);
        }
    } else {
        outln("✓ session_status enum includes 'google classroom'");
    }

    // vedmg_student_enrollments - Check v2.0 changes
    $enrollments_table = $wpdb->prefix . 'vedmg_student_enrollments';
    // Final column state after upgrade/normalization
    outln('Final columns in ' . $sessions_table . ':');
    list_columns($sessions_table);

    outln('Checking vedmg_student_enrollments columns...');

    $enrollment_columns = [
        'instructor_name' => 'VARCHAR(255) DEFAULT NULL',
        'last_scheduled_date' => 'DATETIME DEFAULT NULL',
        'total_sessions_scheduled' => 'INT(11) DEFAULT 0',
        'last_session_type' => "ENUM('individual','group','class_wide') DEFAULT NULL"
    ];

    foreach ($enrollment_columns as $col => $def) {
        check_and_add_column($enrollments_table, $col, $def);
    }

    // Check if enrollment_status column exists (should be removed in v2.0)
    $enrollment_status_exists = $wpdb->get_results("SHOW COLUMNS FROM {$enrollments_table} LIKE 'enrollment_status'");
    if (!empty($enrollment_status_exists)) {
        outln("⚠ enrollment_status column still exists (should be removed in v2.0 upgrade)");
    } else {
        outln("✓ enrollment_status column properly removed");
    }

    // vedmg_courses - Should have all columns from activator + upgrades
    $courses_table = $wpdb->prefix . 'vedmg_courses';
    outln('Checking vedmg_courses columns...');

    $courses_columns = [
        'instructor_name' => 'VARCHAR(255) DEFAULT NULL',
        'class_join_link' => 'TEXT DEFAULT NULL',
        'meeting_link' => 'TEXT DEFAULT NULL'
    ];

    foreach ($courses_columns as $col => $def) {
        check_and_add_column($courses_table, $col, $def);
    }

    // Check for additional tables created by upgrades
    outln('Checking additional tables...');

    $additional_tables = [
        'vedmg_student_classroom_mappings' => 'v1.3',
        'vedmg_session_tracking' => 'v2.0'
    ];

    foreach ($additional_tables as $table => $version) {
        $full_table = $wpdb->prefix . $table;
        $exists = $wpdb->get_var("SHOW TABLES LIKE '{$full_table}'");
        if ($exists === $full_table) {
            outln("✓ Additional table exists: {$full_table} ({$version})");
        } else {
            outln("⚠ Additional table missing: {$full_table} ({$version}) - may be created by upgrade");
        }
    }

} catch (Throwable $e) {
    outln('ERROR during column verification: ' . $e->getMessage());
}

// Step 4: Final verification and summary
outln('Step 4: Final verification...');

// Show current DB version
if (function_exists('get_option')) {
    $db_ver = get_option('vedmg_classroom_db_version', 'unknown');
    outln("Current vedmg_classroom_db_version: {$db_ver}");

    if ($db_ver === '2.2') {
        outln('✓ Database is at latest version (2.2)');
    } else {
        outln("⚠ Database version is {$db_ver}, expected 2.2");
    }
}

// Verify critical columns for admin pages
$critical_checks = [
    [$wpdb->prefix . 'vedmg_class_sessions', 'is_featured'],
    [$wpdb->prefix . 'vedmg_class_sessions', 'featured_date'],
    [$wpdb->prefix . 'vedmg_class_sessions', 'assigned_instructor_id']
];

$all_critical_ok = true;
foreach ($critical_checks as [$table, $column]) {
    $exists = $wpdb->get_results("SHOW COLUMNS FROM {$table} LIKE '{$column}'");
    if (empty($exists)) {
        outln("✗ CRITICAL: Missing {$table}.{$column}");
        $all_critical_ok = false;
    }
}

if ($all_critical_ok) {
    outln('✓ All critical columns for admin pages are present');
} else {
    outln('✗ Some critical columns are missing - admin pages may not work correctly');
}

outln('=== Script completed ===');
outln($upgrade_ok && $all_critical_ok ? 'STATUS: SUCCESS' : 'STATUS: ISSUES DETECTED');

exit($upgrade_ok && $all_critical_ok ? 0 : 1);

